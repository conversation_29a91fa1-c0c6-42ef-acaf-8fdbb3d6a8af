"use client";

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, FileText, StopCircle, Eye, EyeOff, Volume2, VolumeX, Trash } from "lucide-react";
// Import ScriptLine type - it's not directly used here anymore for formatting but might be for other things.
// import { ScriptLine } from "../../lib/tools/scriptFormatter"; // Keep if used elsewhere, otherwise can remove
import ScriptMarkdownContent from "./ScriptMarkdownContent";
import { ScriptFormattingProgress } from "../CircularProgress";
import { useSession } from "next-auth/react";

interface ScriptTabProps {
  scriptContent: string;
  isScriptLoading: boolean;
  isScriptReady: boolean;
  scriptName: string | null;
  scriptId?: string | null; // Add scriptId prop for pre-formatted script lookup
  isListening?: boolean;
  isMuted?: boolean;
  handleEndConversation?: () => Promise<void>;
  toggleMute?: () => void;
  onScriptDeleted?: () => void; // Callback for when script is deleted
}

function ScriptTab({
  scriptContent,
  isScriptLoading,
  isScriptReady,
  scriptName,
  scriptId,
  isListening = false,
  isMuted = false,
  handleEndConversation = async () => {},
  toggleMute = () => {},
  onScriptDeleted = () => {},
}: ScriptTabProps) {
  const [formattedMarkdown, setFormattedMarkdown] = useState<string>("");
  const [isFormatting, setIsFormatting] = useState<boolean>(false);
  const [formatError, setFormatError] = useState<string | null>(null);
  const [showRawScript, setShowRawScript] = useState<boolean>(false);
  const [formattingStage, setFormattingStage] = useState<'uploading' | 'processing' | 'formatting' | 'storing' | 'completed' | 'error'>('formatting');
  const [formattingProgress, setFormattingProgress] = useState<number>(0);
  const [isUsingFallback, setIsUsingFallback] = useState<boolean>(false);
  const [showDeleteModal, setShowDeleteModal] = useState<boolean>(false);
  const [isDeleting, setIsDeleting] = useState<boolean>(false);

  const { data: session } = useSession();

  // Removed direct instantiation of scriptFormatter

  useEffect(() => {
    // Renamed to avoid conflict if an old isFormatting was in scope
    let formattingUnderway = false;

    const loadFormattedScript = async () => {
      if (!isScriptReady || formattingUnderway) {
        return;
      }

      formattingUnderway = true;
      setIsFormatting(true);
      setFormatError(null);
      setFormattedMarkdown(""); // Clear previous markdown
      setFormattingStage('uploading');
      setFormattingProgress(10);

      try {
        // First, try to get the pre-formatted script if we have a scriptId
        if (scriptId) {
          console.log("ScriptTab: Loading pre-formatted script for ID:", scriptId);
          setFormattingStage('processing');
          setFormattingProgress(30);

          const response = await fetch(`/api/get-formatted-script/${scriptId}`);

          if (response.ok) {
            const data = await response.json();
            if (data.success) {
              console.log("ScriptTab: Loaded pre-formatted script:", data.data);
              setFormattingStage('completed');
              setFormattingProgress(100);
              setFormattedMarkdown(data.data.formattedMarkdown);
              return;
            }
          } else if (response.status === 404) {
            console.log("ScriptTab: Pre-formatted script not found, attempting fallback formatting...");
            setFormattingStage('formatting');
            setFormattingProgress(50);
            setIsUsingFallback(true);

            // Try fallback formatting
            const fallbackResponse = await fetch("/api/format-script-fallback", {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({
                scriptId: scriptId,
              }),
            });

            if (fallbackResponse.ok) {
              const fallbackData = await fallbackResponse.json();
              if (fallbackData.success) {
                console.log("ScriptTab: Fallback formatting successful:", fallbackData.data);
                setFormattingStage('storing');
                setFormattingProgress(90);
                setFormattedMarkdown(fallbackData.data.formattedMarkdown);
                setFormattingStage('completed');
                setFormattingProgress(100);
                setIsUsingFallback(false);
                return;
              }
            }
            setIsUsingFallback(false);
          }
        }

        // If no scriptId or pre-formatting failed, fall back to on-demand formatting
        if (!scriptContent) {
          throw new Error("No script content available for formatting");
        }

        console.log("ScriptTab: Using on-demand formatting as final fallback...");
        setFormattingStage('formatting');
        setFormattingProgress(60);

        const response = await fetch('/api/format-script', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ scriptContent }),
        });

        const responseData = await response.json();

        if (!response.ok) {
          console.error("ScriptTab: API Error Response Data:", responseData);
          throw new Error(responseData.details || responseData.error || `API request failed with status ${response.status}`);
        }

        if (!responseData.formattedMarkdown) {
            throw new Error("API response did not include formattedMarkdown.");
        }

        setFormattingStage('storing');
        setFormattingProgress(90);
        setFormattedMarkdown(responseData.formattedMarkdown);
        setFormattingStage('completed');
        setFormattingProgress(100);
        console.log("ScriptTab: On-demand formatting successful.");

      } catch (error) {
        console.error("ScriptTab: Error loading formatted script:", error);
        const errorMessage = error instanceof Error ? error.message : String(error);
        setFormatError(errorMessage);
        setFormattingStage('error');

        // Create fallback markdown with raw content
        const fallbackMarkdown = `## Original Content (Formatting Error)\n\n**Error:** ${errorMessage}\n\n---\n\n${scriptContent
          .split('\n')
          .slice(0, 50)
          .join('\n')}${scriptContent.split('\n').length > 50 ? "\n\n*[...content truncated]*" : ""}`;
        setFormattedMarkdown(fallbackMarkdown);
      } finally {
        setIsFormatting(false);
        setIsUsingFallback(false);
        formattingUnderway = false;
      }
    };

    // Call loadFormattedScript when dependencies change
    loadFormattedScript();

  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [scriptContent, isScriptReady, scriptId]); // Added scriptId to dependencies

  const handleDeleteClick = () => {
    setShowDeleteModal(true);
  };

  const handleDeleteConfirm = async () => {
    if (!scriptId || !session?.user?.email) {
      console.error("Cannot delete: missing scriptId or user session");
      return;
    }

    setIsDeleting(true);
    try {
      const response = await fetch('/api/deleteDocumentAndChats', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: session.user.email,
          namespace: scriptId,
          scriptName: scriptName, // Include script name for ElevenLabs deletion
        }),
      });

      if (response.ok) {
        console.log("Script deleted successfully");
        onScriptDeleted(); // Notify parent component
        setShowDeleteModal(false);
      } else {
        console.error('Failed to delete script:', await response.json());
      }
    } catch (error) {
      console.error('Error deleting script:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  const handleDeleteCancel = () => {
    setShowDeleteModal(false);
  };

  const rawFormattedContent = scriptContent
    .split("\n")
    .map((line, index) => (
      <p key={index} className={`mb-2 ${line.trim() === "" ? "h-4" : ""}`}>
        {line}
      </p>
    ));

  return (
    <div className="h-full flex flex-col">
      {/* Fixed Header */}
      <div className="flex-shrink-0 mb-4 pb-4 border-b border-gray-200 dark:border-white/10 flex justify-between items-center transition-colors duration-300">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white flex items-center transition-colors duration-300">
          <FileText className="w-5 h-5 mr-2 text-gray-500 dark:text-gray-400" />
          Script Content
        </h2>
        <div className="flex items-center space-x-2">
          {/* Delete Script Button */}
          {isScriptReady && !isFormatting && scriptId && scriptName && (
            <button
              onClick={handleDeleteClick}
              className="p-2 bg-red-50 dark:bg-red-600/80 hover:bg-red-100 dark:hover:bg-red-500 text-red-600 dark:text-white rounded-full shadow-sm transition-colors"
              title="Delete script"
            >
              <Trash className="w-5 h-5" />
            </button>
          )}

          {/* Toggle Raw/Formatted Script Button */}
          {isScriptReady && !isFormatting && (
            <button
              onClick={() => setShowRawScript(!showRawScript)}
              className="p-2 bg-gray-100 dark:bg-gray-800/80 hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 rounded-full shadow-sm transition-colors"
              title={showRawScript ? "Show formatted script" : "Show raw script"}
            >
              {showRawScript ? <Eye className="w-5 h-5" /> : <EyeOff className="w-5 h-5" />}
            </button>
          )}

          {/* Rehearsal Controls */}
          {isListening && (
            <>
              <button
                onClick={toggleMute}
                className={`p-2 rounded-full ${
                  isMuted ? "bg-red-500/20 text-red-400" : "bg-green-500/20 text-green-400"
                } hover:bg-opacity-30 transition-colors`}
                title={isMuted ? "Unmute Microphone" : "Mute Microphone"}
              >
                {isMuted ? <VolumeX className="w-4 h-4" /> : <Volume2 className="w-4 h-4" />}
              </button>
              <button
                onClick={() => {
                  const hideSettingsEvent = new CustomEvent("hide-settings-panel");
                  document.dispatchEvent(hideSettingsEvent);
                  if (handleEndConversation) handleEndConversation();
                }}
                className="p-2 bg-red-500/20 text-red-400 hover:bg-red-500/30 rounded-full transition-colors"
                title="End Rehearsal"
              >
                <StopCircle className="w-4 h-4" />
              </button>
            </>
          )}
        </div>
      </div>

      {/* Scrollable Script Content */}
      <div className="flex-grow overflow-y-auto custom-script-scrollbar pr-4 -mr-4 relative">
        {isScriptLoading ? (
          <div className="flex items-center justify-center h-40">
            <Loader className="w-6 h-6 text-gray-500 dark:text-gray-400 animate-spin mr-3" />
            <span className="text-gray-700 dark:text-gray-300">Loading script content...</span>
          </div>
        ) : !isScriptReady || !scriptId ? (
          <div className="text-center py-20">
            <div className="flex flex-col items-center space-y-4">
              <FileText className="w-16 h-16 text-gray-400 dark:text-gray-500" />
              <div className="space-y-2">
                <h3 className="text-lg font-medium text-gray-700 dark:text-gray-300">
                  {!scriptId
                    ? "No Script Selected"
                    : scriptName
                      ? "Script content is being prepared..."
                      : "Loading Script..."}
                </h3>
                <p className="text-gray-500 dark:text-gray-400 max-w-md">
                  {!scriptId
                    ? "Select a script from the sidebar to view its content, or upload a new script to get started."
                    : "Please wait while we prepare your script content."}
                </p>
              </div>
            </div>
          </div>
        ) : (
          <div className="relative">
            {isFormatting ? (
              <div className="flex flex-col items-center justify-center min-h-[400px] py-8 -mt-10">
                <ScriptFormattingProgress
                  stage={formattingStage}
                  progress={formattingProgress}
                />
                <span className="text-gray-600 dark:text-gray-300 -mt-5">Formatting script with CastMate AI</span>
                <span className="text-gray-600 dark:text-gray-300 mt-1">This may take a moment...</span>
                {isUsingFallback && (
                  <span className="text-amber-300 mt-2 font-semibold animate-pulse">
                    Almost Done..
                  </span>
                )}
              </div>
            ) : showRawScript ? (
              <div className="bg-gray-50 dark:bg-gray-900 p-6 rounded-lg shadow-sm mb-4 mx-auto max-w-4xl border border-gray-200 dark:border-gray-700 transition-colors duration-300">
                <div className="text-gray-800 dark:text-gray-200 whitespace-pre-wrap font-mono leading-relaxed">
                  {rawFormattedContent}
                </div>
              </div>
            ) : (
              <div className="markdown-script-output">
                {/* If formattedMarkdown is empty and no error, show a message or keep it blank */}
                {formattedMarkdown || formatError ? (
                    <ScriptMarkdownContent content={formattedMarkdown} />
                ) : (
                    <div className="text-center py-10 text-gray-500 dark:text-gray-400">
                        Ready to display formatted script.
                    </div>
                )}
              </div>
            )}


          </div>
        )}
      </div>

      <style jsx global>{`
        .custom-script-scrollbar::-webkit-scrollbar {
          width: 6px;
        }
        .custom-script-scrollbar::-webkit-scrollbar-track {
          background: rgba(0, 0, 0, 0.1);
          border-radius: 10px;
        }
        .custom-script-scrollbar::-webkit-scrollbar-thumb {
          background: rgba(107, 114, 128, 0.5);
          border-radius: 10px;
        }
        .custom-script-scrollbar::-webkit-scrollbar-thumb:hover {
          background: rgba(107, 114, 128, 0.7);
        }
        .custom-script-scrollbar {
          scrollbar-width: thin;
          scrollbar-color: rgba(107, 114, 128, 0.5) rgba(0, 0, 0, 0.1);
        }

        .markdown-script-output p strong {
          display: block;
          text-align: center;
          font-weight: bold;
          font-size: 1.25rem;
          color: #1f2937; /* Dark gray for character names - works in both themes */
          margin-bottom: 0.5rem;
          margin-top: 2rem;
        }

        .markdown-script-output p:has(strong) {
          margin-top: 2rem;
          margin-bottom: 0.7rem; /* Reduced slightly from 2.7 to tighten with dialogue */
        }

        .markdown-script-output p {
          margin-bottom: 1.3rem;
          line-height: 1.6; /* Slightly increased for readability */
          color: #374151; /* Professional gray for dialogue text */
        }

        .markdown-script-output p em {
          color: #6b7280; /* Professional muted gray for notes */
          display: block;
          margin-top: 0.5rem;
          margin-bottom: 1rem;
          font-style: italic;
        }

        /* Potentially keep for other uses if any */
        .script-dialogue {
          margin-bottom: 1.3rem;
          padding-left: 0.5rem;
          border-left: 2px solid rgba(107, 114, 128, 0.3);
        }
        .script-dialogue strong {
          color: #f59e0b;
          display: inline-block;
          min-width: 120px;
        }
        .line-number {
          color: #6b7280;
          font-weight: normal;
          margin-right: 0.5rem;
          user-select: none;
          display: inline-block;
          min-width: 2rem;
          text-align: right;
        }
        .highlighted-text {
          background-color: #fef3c7;
          color: #000000;
          padding: 0 4px;
        }

        /* Styles for headings and summaries from the markdown */
        .markdown-script-output h1,
        .markdown-script-output h2 {
          color: #111827; /* Professional dark gray for headings */
          margin-top: 1.5em;
          margin-bottom: 0.8em;
          border-bottom: 1px solid #e5e7eb;
          padding-bottom: 0.3em;
        }
        .markdown-script-output h1 {
          font-size: 1.8rem;
        }
        .markdown-script-output h2 {
          font-size: 1.5rem;
        }
        .markdown-script-output ul {
          list-style-type: disc;
          margin-left: 20px;
          margin-bottom: 1em;
          color: #374151;
        }
        .markdown-script-output li {
          margin-bottom: 0.5em;
        }

      `}</style>

      {/* Delete Confirmation Modal */}
      {showDeleteModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/60 backdrop-blur-sm">
          <div className="bg-white dark:bg-slate-800 rounded-xl p-6 max-w-md w-full mx-4 border border-red-200 dark:border-red-500/20 shadow-xl transition-colors duration-300">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Delete Script</h3>
            <p className="text-gray-700 dark:text-gray-300 mb-6">
              Are you sure you want to delete "{scriptName}"? This action cannot be undone and will also delete all associated chats.
            </p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={handleDeleteCancel}
                disabled={isDeleting}
                className="px-4 py-2 bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 text-gray-700 dark:text-white rounded-md transition-colors disabled:opacity-50"
              >
                Cancel
              </button>
              <button
                onClick={handleDeleteConfirm}
                disabled={isDeleting}
                className="px-4 py-2 bg-red-600 hover:bg-red-500 text-white rounded-md transition-colors disabled:opacity-50 flex items-center"
              >
                {isDeleting ? (
                  <>
                    <Loader className="w-4 h-4 animate-spin mr-2" />
                    Deleting...
                  </>
                ) : (
                  'Delete'
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default ScriptTab;