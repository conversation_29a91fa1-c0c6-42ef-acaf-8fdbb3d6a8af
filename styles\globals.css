@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 255, 255, 255;
  --background-end-rgb: 255, 255, 255;

  /* Light theme variables - Professional modern design */
  --theme-bg-primary: 249, 250, 251;       /* gray-50 - Subtle off-white main background */
  --theme-bg-secondary: 255, 255, 255;     /* white - Card/modal backgrounds */
  --theme-bg-tertiary: 243, 244, 246;      /* gray-100 - Subtle section backgrounds */
  --theme-bg-elevated: 255, 255, 255;      /* white - Elevated elements like modals */
  --theme-text-primary: 17, 24, 39;        /* gray-900 - Very dark for primary text */
  --theme-text-secondary: 55, 65, 81;      /* gray-700 - Medium dark for secondary text */
  --theme-text-muted: 107, 114, 128;       /* gray-500 - Muted text with good contrast */
  --theme-border: 229, 231, 235;           /* gray-200 - Subtle borders */
  --theme-border-strong: 209, 213, 219;    /* gray-300 - Stronger borders */
  --theme-accent: 59, 130, 246;            /* blue-500 - Professional accent color */
  --theme-accent-hover: 37, 99, 235;       /* blue-600 - Hover state */
}

.dark {
  --foreground-rgb: 255, 255, 255;
  --background-start-rgb: 0, 0, 0;
  --background-end-rgb: 0, 0, 0;

  /* Dark theme variables - Professional dark design */
  --theme-bg-primary: 3, 7, 18;            /* gray-950 - Nearly black main background */
  --theme-bg-secondary: 17, 24, 39;        /* gray-900 - Card/modal backgrounds */
  --theme-bg-tertiary: 31, 41, 55;         /* gray-800 - Section backgrounds */
  --theme-bg-elevated: 31, 41, 55;         /* gray-800 - Elevated elements */
  --theme-text-primary: 248, 250, 252;     /* slate-50 - Very light for primary text */
  --theme-text-secondary: 203, 213, 225;   /* slate-300 - Light for secondary text */
  --theme-text-muted: 148, 163, 184;       /* slate-400 - Muted text */
  --theme-border: 55, 65, 81;              /* gray-700 - Subtle borders */
  --theme-border-strong: 75, 85, 99;       /* gray-600 - Stronger borders */
  --theme-accent: 96, 165, 250;            /* blue-400 - Bright accent */
  --theme-accent-hover: 147, 197, 253;     /* blue-300 - Hover state */
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

body {
  color: rgb(var(--foreground-rgb));
  background: rgb(var(--background-start-rgb));
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Apply gradient only in dark mode */
.dark body {
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
}

/* Smooth theme transitions */
* {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}

/* Disable transitions during theme change to prevent flash */
.theme-transitioning * {
  transition: none !important;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  @layer components {
    .infoText {
      @apply p-4 bg-gray-700/50 rounded-lg max-w-[300]
    }
}

.chatRow{
  @apply rounded-lg px-5 py-3 text-sm flex items-center
  justify-center space-x-2 hover:bg-gray-700/70 cursor-pointer
  text-gray-300 transition-all duration-200 ease-out

}

.current-message {
  background-color: rgba(255, 182, 193, 0.5); /* Light pink with 50% opacity */
}


.dots {
  display: inline-block;
  width: 1em;
  height: 1em;
  border-radius: 50%;
  background-color: rgb(245, 253, 11); /* Purple dots */
  margin: 0 0.1em;
  animation: dotBounce 1.4s infinite ease-in-out both;
}

.dots:nth-child(1) {
  animation-delay: -0.32s;
}

.dots:nth-child(2) {
  animation-delay: -0.16s;
}

.dots:nth-child(3) {
  animation-delay: 0;
}

.dots-container {
  background-color: transparent;
  padding: 0; /* No padding */
  display: flex;
  align-items: center;
  justify-content: center;
}


@keyframes dotBounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

.dots {
  display: inline-block;
  width: 1em;
  height: 1em;
  border-radius: 50%;
  background-color: rgb(245, 253, 11);; /* Purple dots */
  margin: 0 0.1em;
  animation: dotBounce 1.4s infinite ease-in-out both;
}

.dots-container {
  background-color: transparent;
  padding: 0; /* No padding */
  display: flex;
  align-items: center;
  justify-content: center;
}

@keyframes move-full-left-right {
  0% {
    transform: translateX(0); /* Start at the right */
  }
  20% {
    transform: translateX(-100%); /* Move to the left */
  }
  100% {
    transform: translateX(0); /* Move back to the right */
  }
}

/* Custom Scrollbar Styles for Brand Consistency */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(75, 85, 99, 0.6); /* gray-600 with opacity */
  border-radius: 4px;
  transition: background 0.3s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(75, 85, 99, 0.8); /* gray-600 with higher opacity */
}

.custom-scrollbar::-webkit-scrollbar-corner {
  background: rgba(0, 0, 0, 0.3);
}

/* Firefox scrollbar styling */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(75, 85, 99, 0.6) rgba(0, 0, 0, 0.3);
}

/* Thin scrollbar variant for smaller areas */
.custom-scrollbar-thin::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.custom-scrollbar-thin::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.custom-scrollbar-thin::-webkit-scrollbar-thumb {
  background: rgba(107, 114, 128, 0.5); /* gray-500 with opacity */
  border-radius: 3px;
  transition: background 0.3s ease;
}

.custom-scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: rgba(107, 114, 128, 0.7); /* gray-500 with higher opacity */
}

.custom-scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: rgba(107, 114, 128, 0.5) rgba(0, 0, 0, 0.2);
}

}