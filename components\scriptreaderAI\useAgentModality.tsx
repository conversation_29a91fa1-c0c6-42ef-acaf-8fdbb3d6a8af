import { useState, useCallback, useEffect } from 'react'

export type AgentModalityType = 'conversational' | 'direct'

interface ScriptContext {
  scriptName?: string | null
  scriptContent?: string
  characterInfo?: string[]
  agentName?: string | null
}

interface UseAgentModalityReturn {
  agentModality: AgentModalityType
  setAgentModality: (modality: AgentModalityType) => void
  generatePrompt: (scriptContext?: ScriptContext) => string
  updateAgentPrompt: (agentId: string, apiKey: string, scriptContext?: ScriptContext) => Promise<any>
}

/**
 * Hook for managing ElevenLabs agent modality and prompt generation
 * Provides two distinct prompt modes: Conversational (coaching) and Direct (professional)
 */
export function useAgentModality(): UseAgentModalityReturn {
  const [agentModality, setAgentModality] = useState<AgentModalityType>('conversational')

  // Log hook initialization
  useEffect(() => {
    console.log(`[AGENT_MODALITY] 🚀 useAgentModality hook initialized with default modality: "${agentModality}"`);
  }, []);

  // Enhanced setAgentModality with logging
  const setAgentModalityWithLogging = useCallback((newModality: AgentModalityType) => {
    console.log(`[AGENT_MODALITY] 🔄 Modality state change requested: "${agentModality}" → "${newModality}"`);

    if (newModality === agentModality) {
      console.log(`[AGENT_MODALITY] No change needed - already in "${newModality}" mode`);
      return;
    }

    console.log(`[AGENT_MODALITY] ✅ Modality state updated to: "${newModality}"`);
    setAgentModality(newModality);
  }, [agentModality])

  /**
   * Generates conversational mode prompt (Prompt Version 2)
   * Supportive coaching style with comprehensive feedback
   */
  const generateConversationalPrompt = useCallback((scriptContext?: ScriptContext): string => {
    // Extract agent name with fallback
    const agentName = scriptContext?.agentName || 'CastMate Assistant';

    console.log(`[AGENT_MODALITY] 🏷️ Injecting agent name into conversational prompt: "${agentName}"`);

    const basePrompt = `Castmate Rehearsal Partner: Comprehensive Performance Enhancement System

Core Purpose and Identity
You are an advanced AI Script/Rehearsal Assistant designed to help actors practice, refine, and perfect their performance. You are both a reliable scene partner and a specialized performance coach, dedicated to helping actors grow their craft in a supportive and engaging environment. You can introduce yourself by this persona.

Technical Capabilities and Methodologies
Script Processing and Management:
- Parse and process any script format (screenplay, stage play, sides, etc.).
- Maintain complete script awareness including structure, character relationships, and narrative arc.
- Identify specific line numbers, page references, and scene markers.

Performance Analysis System:
- Tonality Assessment: Monitor vocal pitch variations, emotional coloring, and inconsistencies with character objectives.
- Cadence Evaluation: Analyze speech rhythm, pacing, timing, and breath control.
- Delivery Quality Metrics: Evaluate articulation, volume, and consistency of the character's voice.

Line Reading Support
- Provide character-appropriate line readings with adjustable emotional intensity.
- Offer multiple interpretation options for pivotal lines to explore creative choices.
- Simulate realistic interaction timing, including overlaps and interruptions where appropriate.

Operational Protocols
Initialization Protocol: You will begin each session with a welcoming and thorough setup:
- Start with a warm welcome and introduce yourself as "${agentName}". State the name of the script(s) you have access to.
- Guide the user to establish which script they want to rehearse and which character(s) they are playing.
- Help them identify a clear starting point.
- Ask if there is a specific aspect of their performance they would like to work on (e.g., finding the character's motivation, emotional transitions, line memorization).
- Confirm whether they would like a detailed performance appraisal at the end of the scene.
- Set default feedback to be delivered at the end of each scene unless they prefer immediate correction.

Active Rehearsal Mode:
- Before the actor's first line, you will initiate a 3-second countdown: "Ready... 3... 2... 1..."
- Read opposite lines with appropriate emotion, pace, and intensity.
- Maintain consistent and distinct character voices for multi-character scenes.
- Respond dynamically to the actor's delivery choices.

Performance Enhancement and Feedback:
- Performance Enhancement Loop: You will actively identify strengths to build confidence, highlight areas for improvement, suggest targeted exercises (e.g., for breath control, emotional recall), and track their progress across rehearsals.
- Appraisal Accuracy Mandate: All appraisals MUST be honest and accurate to ensure genuine growth:
  - Line Accuracy: You will gently but clearly point out ALL mistakes in the lines. For memorization focus, this will be your primary feedback.
  - Tonality: You will assess tonality against the EXACT requirements of the character's objectives, scene context, and sentiment, explaining why a certain choice might be more effective.
  - Honest Assessment: You will commend the user for performances that are of the required standard and clearly identify areas needing improvement, ensuring they trust your guidance.
- Feedback is structured to be encouraging yet constructive, providing specific examples from the performance and offering alternative approaches for them to try.

Interaction Design
Communication Style: Your demeanor is supportive, encouraging, and professional. You adapt your feedback intensity based on the actor's experience and preferences. You use industry terminology but are always ready to explain concepts clearly.
Adaptive Response System: You calibrate to the actor's learning style, adjusting the level of technical detail and support intensity based on their confidence and progress.
Reporting: You can generate progress reports that highlight growth areas and track improvement over time.

Implementation Guidelines:
- Initialize each session with the welcoming Initialization Protocol.
- Process the script completely before beginning.
- Implement comprehensive markdown formatting for all responses:
  - Use **bold** for performance directions
  - Use *italics* for emotional cues
  - Use blockquotes for script lines
  - Use headers for scene transitions
  - Use bullet points for itemized feedback
  - Use code blocks for technical instruction or exercises`

    // Add script-specific context if provided
    if (scriptContext?.scriptName || scriptContext?.scriptContent) {
      const scriptInfo = `

CURRENT SCRIPT CONTEXT:
- Script Name: ${scriptContext.scriptName || 'Unknown Script'}
- Script Available: ${!!scriptContext.scriptContent}
${scriptContext.characterInfo?.length ? `- Characters Detected: ${scriptContext.characterInfo.join(', ')}` : ''}

Please adapt your supportive coaching approach to this specific script content and help the actor rehearse effectively with encouraging, detailed feedback.`

      return basePrompt + scriptInfo
    }

    return basePrompt
  }, [])

  /**
   * Generates direct mode prompt (Prompt Version 1)
   * Professional, minimal interaction style focused on precision
   */
  const generateDirectPrompt = useCallback((scriptContext?: ScriptContext): string => {
    // Extract agent name with fallback
    const agentName = scriptContext?.agentName || 'CastMate Assistant';

    console.log(`[AGENT_MODALITY] 🏷️ Injecting agent name into direct prompt: "${agentName}"`);

    const basePrompt = `Castmate Rehearsal Partner: Precision Performance System

Core Purpose and Identity
You are a precision AI Rehearsal Partner. Your sole function is to facilitate efficient and technically focused rehearsals for advanced and professional actors. You serve as a reliable, data-driven scene partner, prioritizing accuracy, timing, and user-defined objectives.

Technical Capabilities and Methodologies
Script Processing and Management:
- Parse and process any script format (screenplay, stage play, sides, etc.).
- Maintain complete script awareness including structure, character, and narrative.
- Identify specific line numbers, page references, and scene markers for precision.

Performance Analysis System:
- Tonality Assessment: Monitor vocal pitch and emotional coloring against script requirements.
- Cadence Evaluation: Analyze speech rhythm, pacing, and timing for dramatic effectiveness.
- Delivery Quality Metrics: Evaluate articulation, pronunciation, and volume modulation.

Line Reading Support
- Provide character-appropriate line readings with precise emotional intensity.
- Implement variable pacing as directed (e.g., slow for memorization, natural for performance).
- Simulate realistic interaction timing.

Operational Protocols
Initialization Protocol: Your initialization is concise and efficient. You will:
- Start by stating your name as "${agentName}" eg 'Hi I'm ${agentName} from CastMate'
- State the name of the script(s) you have access to.
- Confirm whether the user is ready to begin

Active Rehearsal Mode:
- Before the actor's first line, you will initiate a 3-second countdown: "Ready... 3... 2... 1..."
- Read opposite lines with the specified pace and intensity.
- Maintain distinct and consistent character voices.
- Follow standard rehearsal notation ("pickup from," "from the top," etc.) precisely.
- Respond dynamically to the actor's delivery choices.
- Apply all instructions as specified by the current actor

Feedback and Appraisal Protocol:
- Feedback is delivered only upon request at the end of a scene or session.
- Appraisals are technical, objective, and data-driven, focused strictly on the agreed-upon parameters.
- Appraisal Accuracy Mandate: All appraisals MUST be rigorously accurate:
  - Line Accuracy: You will report on ALL deviations from the script, including added, omitted, or substituted words.
  - Tonality: You will assess tonality by comparing the user's delivery to the EXACT emotional and contextual requirements of the character and scene.
  - Direct Assessment: Your analysis will be direct. You will clearly state where the performance met the standard and where it did not, providing concrete examples.

Interaction Design
Communication Style: Your demeanor is formal, professional, and efficient. You will avoid conversational filler, praise, or unsolicited advice. Your language is technical and direct.
Adaptive Response: You adapt by strictly adhering to the user-defined parameters established during initialization. You do not deviate from the established focus or offer creative suggestions unless explicitly asked.
Reporting: You can provide quantitative performance metrics and track technical patterns across sessions if requested.

Implementation Guidelines:
- Initialize each session by executing the concise Initialization Protocol.
- Process the script completely before beginning.
- Implement comprehensive markdown formatting for all responses:
  - Use **bold** for performance directions
  - Use *italics* for emotional cues
  - Use blockquotes for script lines
  - Use headers for scene transitions
  - Use bullet points for itemized feedback`

    // Add script-specific context if provided
    if (scriptContext?.scriptName || scriptContext?.scriptContent) {
      const scriptInfo = `

CURRENT SCRIPT CONTEXT:
- Script Name: ${scriptContext.scriptName || 'Unknown Script'}
- Script Available: ${!!scriptContext.scriptContent}
${scriptContext.characterInfo?.length ? `- Characters Detected: ${scriptContext.characterInfo.join(', ')}` : ''}

Maintain your professional, direct approach while working with this specific script content.`

      return basePrompt + scriptInfo
    }

    return basePrompt
  }, [])

  /**
   * Generates the appropriate prompt based on current modality
   */
  const generatePrompt = useCallback((scriptContext?: ScriptContext): string => {
    console.log(`[AGENT_MODALITY] Starting prompt generation with modality: "${agentModality}"`);
    console.log(`[AGENT_MODALITY] Script context provided:`, {
      hasScriptName: !!scriptContext?.scriptName,
      scriptName: scriptContext?.scriptName || 'No script name',
      hasScriptContent: !!scriptContext?.scriptContent,
      scriptContentLength: scriptContext?.scriptContent?.length || 0,
      characterCount: scriptContext?.characterInfo?.length || 0,
      characters: scriptContext?.characterInfo || [],
      hasAgentName: !!scriptContext?.agentName,
      agentName: scriptContext?.agentName || 'CastMate Assistant (default)'
    });

    let generatedPrompt: string;

    if (agentModality === 'conversational') {
      console.log(`[AGENT_MODALITY] Generating Conversational Mode prompt (Version 2)...`);
      generatedPrompt = generateConversationalPrompt(scriptContext);
    } else {
      console.log(`[AGENT_MODALITY] Generating Direct Mode prompt (Version 1)...`);
      generatedPrompt = generateDirectPrompt(scriptContext);
    }

    console.log(`[AGENT_MODALITY] ✅ Prompt generation completed:`, {
      modality: agentModality,
      promptLength: generatedPrompt.length,
      promptPreview: generatedPrompt.substring(0, 200) + '...',
      scriptContext: scriptContext?.scriptName || 'No script context'
    });

    // Log the full prompt in a collapsed group for debugging
    console.groupCollapsed(`[AGENT_MODALITY] Full ${agentModality} prompt content`);
    console.log(generatedPrompt);
    console.groupEnd();

    return generatedPrompt;
  }, [agentModality, generateConversationalPrompt, generateDirectPrompt])

  /**
   * Updates the ElevenLabs agent prompt using the current modality
   */
  const updateAgentPrompt = useCallback(async (
    agentId: string,
    apiKey: string,
    scriptContext?: ScriptContext
  ): Promise<any> => {
    console.log(`[AGENT_MODALITY] 🚀 Starting agent prompt update process`);
    console.log(`[AGENT_MODALITY] Configuration:`, {
      agentId: agentId,
      modality: agentModality,
      hasApiKey: !!apiKey,
      scriptName: scriptContext?.scriptName || 'No script',
      scriptContentLength: scriptContext?.scriptContent?.length || 0,
      characterCount: scriptContext?.characterInfo?.length || 0
    });

    try {
      // Generate the appropriate prompt
      console.log(`[AGENT_MODALITY] Step 1: Generating ${agentModality} prompt...`);
      const newPrompt = generatePrompt(scriptContext);

      console.log(`[AGENT_MODALITY] Step 2: Importing ElevenLabs functions...`);
      // Import the ElevenLabs functions dynamically
      const { getAgentConfiguration, createElevenLabsClient } = await import('./elevenlabs');

      console.log(`[AGENT_MODALITY] Step 3: Retrieving current agent configuration...`);
      // Get current agent configuration
      const currentConfig = await getAgentConfiguration(agentId, apiKey);
      console.log(`[AGENT_MODALITY] Current agent configuration retrieved successfully`);

      // Log current vs new prompt comparison
      const currentPrompt = currentConfig.conversation_config?.agent?.prompt?.prompt ||
                           currentConfig.agent?.prompt?.prompt || '';
      console.log(`[AGENT_MODALITY] Prompt comparison:`, {
        currentPromptLength: currentPrompt.length,
        newPromptLength: newPrompt.length,
        promptChanged: currentPrompt !== newPrompt,
        modality: agentModality
      });

      console.log(`[AGENT_MODALITY] Step 4: Preparing update payload...`);
      // Prepare update payload
      const conversationConfig = currentConfig.conversation_config || currentConfig;
      const patchBody = {
        conversation_config: {
          ...conversationConfig,
          agent: {
            ...conversationConfig.agent,
            prompt: {
              ...conversationConfig.agent?.prompt,
              prompt: newPrompt
            }
          }
        }
      };

      console.log(`[AGENT_MODALITY] Step 5: Executing agent update...`);
      console.log(`[AGENT_MODALITY] Update details:`, {
        modality: agentModality,
        promptLength: newPrompt.length,
        agentId: agentId,
        scriptContext: scriptContext?.scriptName || 'No script context'
      });

      // Log prompt preview for debugging
      console.groupCollapsed(`[AGENT_MODALITY] Prompt being sent to ElevenLabs API (${agentModality} mode)`);
      console.log('Prompt preview (first 500 chars):', newPrompt.substring(0, 500) + '...');
      console.log('Full prompt length:', newPrompt.length, 'characters');
      console.groupEnd();

      // Execute the update
      const client = createElevenLabsClient(apiKey);
      const updateResult = await client.conversationalAi.updateAgent(agentId, patchBody);

      console.log(`[AGENT_MODALITY] ✅ Agent prompt update completed successfully!`);
      console.log(`[AGENT_MODALITY] Update result:`, {
        success: true,
        modality: agentModality,
        promptLength: newPrompt.length,
        agentId: agentId,
        scriptName: scriptContext?.scriptName || 'No script'
      });

      return updateResult;

    } catch (error) {
      console.error(`[AGENT_MODALITY] ❌ Failed to update agent prompt:`, {
        error: error,
        modality: agentModality,
        agentId: agentId,
        scriptName: scriptContext?.scriptName || 'No script'
      });
      throw error;
    }
  }, [agentModality, generatePrompt])

  return {
    agentModality,
    setAgentModality: setAgentModalityWithLogging,
    generatePrompt,
    updateAgentPrompt
  }
}
